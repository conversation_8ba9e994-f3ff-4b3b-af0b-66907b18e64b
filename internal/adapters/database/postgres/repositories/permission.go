package repositories

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PostgresPermissionRepository implements the permission repository interface
type PostgresPermissionRepository struct {
	db *gorm.DB
}

// NewPostgresPermissionRepository creates a new permission repository
func NewPostgresPermissionRepository(db *gorm.DB) repositories.PermissionRepository {
	return &PostgresPermissionRepository{db: db}
}

// Create creates a new permission
func (r *PostgresPermissionRepository) Create(ctx context.Context, permission *entities.Permission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

// GetByID gets a permission by ID
func (r *PostgresPermissionRepository) GetByID(ctx context.Context, id string) (*entities.Permission, error) {
	var permission entities.Permission

	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Preload("User").
		Preload("Granter").
		First(&permission).Error

	if err != nil {
		return nil, err
	}

	return &permission, nil
}

// Update updates an existing permission
func (r *PostgresPermissionRepository) Update(ctx context.Context, permission *entities.Permission) error {
	return r.db.WithContext(ctx).Save(permission).Error
}

// Delete deletes a permission by ID
func (r *PostgresPermissionRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&entities.Permission{}, "id = ?", id).Error
}

// GetUserPermissions gets all active permissions for a user
func (r *PostgresPermissionRepository) GetUserPermissions(ctx context.Context, userID string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Preload("User").
		Preload("Granter").
		Find(&permissions).Error

	return permissions, err
}

// GetPermissionsByScope gets permissions by scope pattern
func (r *PostgresPermissionRepository) GetPermissionsByScope(ctx context.Context, scopePattern string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Where("scope LIKE ?", scopePattern).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Preload("User").
		Preload("Granter").
		Find(&permissions).Error

	return permissions, err
}

// GetUsersWithPermission gets all users who have a specific permission
func (r *PostgresPermissionRepository) GetUsersWithPermission(ctx context.Context, scope, resourceType, action string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	query := r.db.WithContext(ctx).
		Where("expires_at IS NULL OR expires_at > ?", time.Now())

	// Scope matching
	if scope != "" {
		query = query.Where("scope = ? OR scope = ?", scope, entities.ScopeSystem)
	}

	// Resource type matching
	if resourceType != "" {
		query = query.Where("resource_type = ? OR resource_type = ?", resourceType, entities.ResourceTypeAll)
	}

	// Action matching (check if action is in the actions array)
	if action != "" {
		query = query.Where("? = ANY(actions) OR ? = ANY(actions)", action, entities.ActionAll)
	}

	err := query.
		Preload("User").
		Preload("Granter").
		Find(&permissions).Error

	return permissions, err
}

// GrantPermission grants a permission to a user
func (r *PostgresPermissionRepository) GrantPermission(ctx context.Context, userID uuid.UUID, scope, resourceType string, actions []string, grantedBy *string, expiresAt *time.Time) (*entities.Permission, error) {
	permission := &entities.Permission{
		UserID:       userID,
		Scope:        scope,
		ResourceType: resourceType,
		Actions:      actions,
		GrantedBy:    grantedBy,
		GrantedAt:    time.Now(),
		ExpiresAt:    expiresAt,
	}

	err := r.Create(ctx, permission)
	if err != nil {
		return nil, err
	}

	return permission, nil
}

// RevokePermission revokes a specific permission
func (r *PostgresPermissionRepository) RevokePermission(ctx context.Context, userID, scope, resourceType string) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND scope = ? AND resource_type = ?", userID, scope, resourceType).
		Delete(&entities.Permission{}).Error
}

// RevokeUserPermissions revokes all permissions for a user
func (r *PostgresPermissionRepository) RevokeUserPermissions(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&entities.Permission{}).Error
}

// GetExpiredPermissions gets all expired permissions
func (r *PostgresPermissionRepository) GetExpiredPermissions(ctx context.Context) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Preload("User").
		Preload("Granter").
		Find(&permissions).Error

	return permissions, err
}

// CleanExpiredPermissions removes all expired permissions
func (r *PostgresPermissionRepository) CleanExpiredPermissions(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Delete(&entities.Permission{})

	return result.RowsAffected, result.Error
}

// HasPermission checks if a user has a specific permission
func (r *PostgresPermissionRepository) HasPermission(ctx context.Context, userID, scope, resourceType, action string) (bool, error) {
	var count int64

	err := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Where("user_id = ?", userID).
		Where("scope = ? OR scope = ?", scope, entities.ScopeSystem).
		Where("resource_type = ? OR resource_type = ?", resourceType, entities.ResourceTypeAll).
		Where("? = ANY(actions) OR ? = ANY(actions)", action, entities.ActionAll).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Count(&count).Error

	return count > 0, err
}
