-- Add missing policy fields to match entity definition
-- This migration adds all the missing policy configuration fields
-- that exist in the Policy entity but are missing from the database schema

-- Add missing policy configuration fields
ALTER TABLE policies ADD COLUMN IF NOT EXISTS lock_version INTEGER DEFAULT 0;

-- Heartbeat configuration fields
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_heartbeat BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS heartbeat_duration INTEGER;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS heartbeat_basis VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS heartbeat_cull_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS heartbeat_resurrection_strategy VARCHAR(255);

-- Check-in configuration fields
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_check_in BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS check_in_interval VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS check_in_interval_count INTEGER;

-- Strategy configuration fields
ALTER TABLE policies ADD COLUMN IF NOT EXISTS machine_uniqueness_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS machine_matching_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS machine_leasing_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS component_uniqueness_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS component_matching_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS process_leasing_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS expiration_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS expiration_basis VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS renewal_basis VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS authentication_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS transfer_strategy VARCHAR(255);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS overage_strategy VARCHAR(255);

-- Scope requirement fields
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_product_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_policy_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_machine_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_fingerprint_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_user_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_checksum_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_version_scope BOOLEAN DEFAULT FALSE;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS require_components_scope BOOLEAN DEFAULT FALSE;
