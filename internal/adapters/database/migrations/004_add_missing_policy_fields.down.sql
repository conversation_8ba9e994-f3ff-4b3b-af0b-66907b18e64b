-- Remove the policy fields added in the up migration
-- This reverts the schema back to the previous state

-- Remove scope requirement fields
ALTER TABLE policies DROP COLUMN IF EXISTS require_components_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_version_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_checksum_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_user_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_fingerprint_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_machine_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_policy_scope;
ALTER TABLE policies DROP COLUMN IF EXISTS require_product_scope;

-- Remove strategy configuration fields
ALTER TABLE policies DROP COLUMN IF EXISTS overage_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS transfer_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS authentication_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS renewal_basis;
ALTER TABLE policies DROP COLUMN IF EXISTS expiration_basis;
ALTER TABLE policies DROP COLUMN IF EXISTS expiration_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS process_leasing_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS component_matching_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS component_uniqueness_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS machine_leasing_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS machine_matching_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS machine_uniqueness_strategy;

-- Remove check-in configuration fields
ALTER TABLE policies DROP COLUMN IF EXISTS check_in_interval_count;
ALTER TABLE policies DROP COLUMN IF EXISTS check_in_interval;
ALTER TABLE policies DROP COLUMN IF EXISTS require_check_in;

-- Remove heartbeat configuration fields
ALTER TABLE policies DROP COLUMN IF EXISTS heartbeat_resurrection_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS heartbeat_cull_strategy;
ALTER TABLE policies DROP COLUMN IF EXISTS heartbeat_basis;
ALTER TABLE policies DROP COLUMN IF EXISTS heartbeat_duration;
ALTER TABLE policies DROP COLUMN IF EXISTS require_heartbeat;

-- Remove lock version field
ALTER TABLE policies DROP COLUMN IF EXISTS lock_version;
