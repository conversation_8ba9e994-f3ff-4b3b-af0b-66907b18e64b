-- Remove the license and machine fields added in the up migration
-- This reverts the schema back to the previous state

-- ==================================================
-- Drop indexes first
-- ==================================================
DROP INDEX IF EXISTS idx_machines_last_check_out_new;
DROP INDEX IF EXISTS idx_machines_heartbeat_jid;
DROP INDEX IF EXISTS idx_machines_next_heartbeat_new;
DROP INDEX IF EXISTS idx_machines_last_heartbeat_new;
DROP INDEX IF EXISTS idx_machines_last_seen_new;
DROP INDEX IF EXISTS idx_machines_deactivated_at;
DROP INDEX IF EXISTS idx_machines_activated_at;
DROP INDEX IF EXISTS idx_licenses_expiration_events;
DROP INDEX IF EXISTS idx_licenses_last_check_out;

-- ==================================================
-- MACHINES - Remove added fields
-- ==================================================
ALTER TABLE machines DROP COLUMN IF EXISTS last_check_out_at;
ALTER TABLE machines DROP COLUMN IF EXISTS heartbeat_jid;
ALTER TABLE machines DROP COLUMN IF EXISTS last_death_event_sent_at;
ALTER TABLE machines DROP COLUMN IF EXISTS next_heartbeat_at;
ALTER TABLE machines DROP COLUMN IF EXISTS last_heartbeat_at;
ALTER TABLE machines DROP COLUMN IF EXISTS last_seen;
ALTER TABLE machines DROP COLUMN IF EXISTS deactivated_at;
ALTER TABLE machines DROP COLUMN IF EXISTS activated_at;

-- ==================================================
-- LICENSES - Remove added fields
-- ==================================================
ALTER TABLE licenses DROP COLUMN IF EXISTS last_check_in_soon_event_sent_at;
ALTER TABLE licenses DROP COLUMN IF EXISTS last_expiring_soon_event_sent_at;
ALTER TABLE licenses DROP COLUMN IF EXISTS last_check_in_event_sent_at;
ALTER TABLE licenses DROP COLUMN IF EXISTS last_expiration_event_sent_at;
ALTER TABLE licenses DROP COLUMN IF EXISTS last_check_out_at;
