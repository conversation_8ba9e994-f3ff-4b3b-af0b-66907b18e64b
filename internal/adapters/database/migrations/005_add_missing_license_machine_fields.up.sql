-- Add missing license and machine fields to match entity definitions
-- This migration adds missing tracking and event fields

-- ==================================================
-- LICENSES - Add missing event tracking fields
-- ==================================================
ALTER TABLE licenses ADD COLUMN IF NOT EXISTS last_check_out_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE licenses ADD COLUMN IF NOT EXISTS last_expiration_event_sent_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE licenses ADD COLUMN IF NOT EXISTS last_check_in_event_sent_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE licenses ADD COLUMN IF NOT EXISTS last_expiring_soon_event_sent_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE licenses ADD COLUMN IF NOT EXISTS last_check_in_soon_event_sent_at TIMESTAMP WITH TIME ZONE;

-- ==================================================
-- MACHINES - Add missing lifecycle and heartbeat fields
-- ==================================================
ALTER TABLE machines ADD COLUMN IF NOT EXISTS activated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE machines ADD COLUMN IF NOT EXISTS deactivated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE machines ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP WITH TIME ZONE;
ALTER TABLE machines ADD COLUMN IF NOT EXISTS last_heartbeat_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE machines ADD COLUMN IF NOT EXISTS next_heartbeat_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE machines ADD COLUMN IF NOT EXISTS last_death_event_sent_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE machines ADD COLUMN IF NOT EXISTS heartbeat_jid VARCHAR(255);
ALTER TABLE machines ADD COLUMN IF NOT EXISTS last_check_out_at TIMESTAMP WITH TIME ZONE;

-- ==================================================
-- Add indexes for the new fields to maintain performance
-- ==================================================

-- License event tracking indexes
CREATE INDEX IF NOT EXISTS idx_licenses_last_check_out ON licenses(last_check_out_at) WHERE last_check_out_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_licenses_expiration_events ON licenses(last_expiration_event_sent_at) WHERE last_expiration_event_sent_at IS NOT NULL;

-- Machine lifecycle indexes
CREATE INDEX IF NOT EXISTS idx_machines_activated_at ON machines(activated_at) WHERE activated_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_machines_deactivated_at ON machines(deactivated_at) WHERE deactivated_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_machines_last_seen_new ON machines(last_seen) WHERE last_seen IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_machines_last_heartbeat_new ON machines(last_heartbeat_at) WHERE last_heartbeat_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_machines_next_heartbeat_new ON machines(next_heartbeat_at) WHERE next_heartbeat_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_machines_heartbeat_jid ON machines(heartbeat_jid) WHERE heartbeat_jid IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_machines_last_check_out_new ON machines(last_check_out_at) WHERE last_check_out_at IS NOT NULL;
