package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// OrganizationHandler handles HTTP requests for organization operations
type OrganizationHandler struct {
	organizationRepo repositories.OrganizationRepository
}

// NewOrganizationHandler creates a new organization handler
func NewOrganizationHandler(
	organizationRepo repositories.OrganizationRepository,
) *OrganizationHandler {
	return &OrganizationHandler{
		organizationRepo: organizationRepo,
	}
}

// Request types - simple, flat Go structs
type CreateOrganizationRequest struct {
	Name  string `json:"name" binding:"required"`
	Slug  string `json:"slug" binding:"required"`
	Email string `json:"email" binding:"required,email"`

	// Optional fields
	Type      string `json:"type,omitempty"`
	Status    string `json:"status,omitempty"`
	Protected bool   `json:"protected,omitempty"`

	// Resource limits
	MaxUsers    *int `json:"maxUsers,omitempty"`
	MaxLicenses *int `json:"maxLicenses,omitempty"`
	MaxMachines *int `json:"maxMachines,omitempty"`

	// Configuration
	Settings map[string]any `json:"settings,omitempty"`
	Metadata map[string]any `json:"metadata,omitempty"`
}

type UpdateOrganizationRequest struct {
	Name  string `json:"name,omitempty"`
	Slug  string `json:"slug,omitempty"`
	Email string `json:"email,omitempty"`

	// Optional fields
	Type      string `json:"type,omitempty"`
	Status    string `json:"status,omitempty"`
	Protected *bool  `json:"protected,omitempty"`

	// Resource limits
	MaxUsers    *int `json:"maxUsers,omitempty"`
	MaxLicenses *int `json:"maxLicenses,omitempty"`
	MaxMachines *int `json:"maxMachines,omitempty"`

	// Configuration
	Settings map[string]any `json:"settings,omitempty"`
	Metadata map[string]any `json:"metadata,omitempty"`
}

// Response types - simple, flat Go structs
type OrganizationResponse struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Slug  string `json:"slug"`
	Email string `json:"email"`

	// Organization type and status
	Type      string `json:"type"`
	Status    string `json:"status"`
	Protected bool   `json:"protected"`

	// Public keys (private keys are never exposed)
	PublicKey        *string `json:"publicKey,omitempty"`
	Ed25519PublicKey *string `json:"ed25519PublicKey,omitempty"`

	// Resource limits
	MaxUsers    *int `json:"maxUsers,omitempty"`
	MaxLicenses *int `json:"maxLicenses,omitempty"`
	MaxMachines *int `json:"maxMachines,omitempty"`

	// Configuration
	Settings map[string]any `json:"settings"`
	Metadata map[string]any `json:"metadata"`

	// Timestamps
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

type OrganizationListResponse struct {
	Organizations []OrganizationResponse `json:"organizations"`
	Total         int64                  `json:"total"`
	Page          int                    `json:"page"`
	Limit         int                    `json:"limit"`
}

// GetOrganizations handles GET /organizations - list organizations with pagination
func (h *OrganizationHandler) GetOrganizations(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "25"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 25
	}

	filter := repositories.ListFilter{
		PageSize: limit,
		Page:     page,
	}

	// Fetch organizations from repository
	organizations, total, err := h.organizationRepo.List(c.Request.Context(), filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch organizations")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch organizations",
			},
		})
		return
	}

	// Convert to response format
	organizationResponses := make([]OrganizationResponse, len(organizations))
	for i, organization := range organizations {
		organizationResponses[i] = h.entityToResponse(organization)
	}

	c.JSON(http.StatusOK, OrganizationListResponse{
		Organizations: organizationResponses,
		Total:         total,
		Page:          page,
		Limit:         limit,
	})
}

// GetOrganization handles GET /organizations/:id - get single organization
func (h *OrganizationHandler) GetOrganization(c *gin.Context) {
	organizationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_ORGANIZATION_ID",
				Message: "Invalid organization ID format",
			},
		})
		return
	}

	organization, err := h.organizationRepo.GetByID(c.Request.Context(), organizationID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "ORGANIZATION_NOT_FOUND",
					Message: "Organization not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to fetch organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch organization",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(organization))
}

// CreateOrganization handles POST /organizations - create new organization
func (h *OrganizationHandler) CreateOrganization(c *gin.Context) {
	var req CreateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Check if slug already exists
	existingOrganization, err := h.organizationRepo.GetBySlug(c.Request.Context(), req.Slug)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error().Err(err).Str("slug", req.Slug).Msg("Failed to check organization slug")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate organization slug",
			},
		})
		return
	}
	if existingOrganization != nil {
		c.JSON(http.StatusConflict, ErrorResponse{
			Error: ErrorDetail{
				Code:    "ORGANIZATION_SLUG_EXISTS",
				Message: "Organization with this slug already exists",
			},
		})
		return
	}

	// Create organization entity
	organization := h.requestToEntity(&req)

	// Save to database
	if err := h.organizationRepo.Create(c.Request.Context(), organization); err != nil {
		log.Error().Err(err).Msg("Failed to create organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create organization",
			},
		})
		return
	}

	c.JSON(http.StatusCreated, h.entityToResponse(organization))
}

// UpdateOrganization handles PUT /organizations/:id - update organization
func (h *OrganizationHandler) UpdateOrganization(c *gin.Context) {
	organizationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_ORGANIZATION_ID",
				Message: "Invalid organization ID format",
			},
		})
		return
	}

	var req UpdateOrganizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Fetch existing organization
	organization, err := h.organizationRepo.GetByID(c.Request.Context(), organizationID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "ORGANIZATION_NOT_FOUND",
					Message: "Organization not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to fetch organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch organization",
			},
		})
		return
	}

	// If slug is being updated, check for conflicts
	if req.Slug != "" && req.Slug != organization.Slug {
		existingOrganization, err := h.organizationRepo.GetBySlug(c.Request.Context(), req.Slug)
		if err != nil && err != gorm.ErrRecordNotFound {
			log.Error().Err(err).Str("slug", req.Slug).Msg("Failed to check organization slug")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate organization slug",
				},
			})
			return
		}
		if existingOrganization != nil {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error: ErrorDetail{
					Code:    "ORGANIZATION_SLUG_EXISTS",
					Message: "Organization with this slug already exists",
				},
			})
			return
		}
	}

	// Update organization fields
	h.updateEntityFromRequest(organization, &req)

	// Save to database
	if err := h.organizationRepo.Update(c.Request.Context(), organization); err != nil {
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to update organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update organization",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(organization))
}

// DeleteOrganization handles DELETE /organizations/:id - delete organization
func (h *OrganizationHandler) DeleteOrganization(c *gin.Context) {
	organizationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_ORGANIZATION_ID",
				Message: "Invalid organization ID format",
			},
		})
		return
	}

	// Check if organization exists and is not protected
	organization, err := h.organizationRepo.GetByID(c.Request.Context(), organizationID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "ORGANIZATION_NOT_FOUND",
					Message: "Organization not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to fetch organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch organization",
			},
		})
		return
	}

	// Check if organization is protected
	if organization.Protected {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error: ErrorDetail{
				Code:    "ORGANIZATION_PROTECTED",
				Message: "Cannot delete protected organization",
			},
		})
		return
	}

	// Soft delete the organization
	if err := h.organizationRepo.SoftDelete(c.Request.Context(), organizationID); err != nil {
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to delete organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to delete organization",
			},
		})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}
