package entities

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// Permission represents pure resource-based permissions
// This is the core of the new authorization system
type Permission struct {
	ID     uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`

	// Scope defines WHERE the permission applies
	// Examples: "system", "org:xxx", "resource:product:xxx", "owner"
	Scope string `json:"scope" gorm:"size:255;not null"`

	// ResourceType defines WHAT type of resource
	// Examples: "*", "product", "license", "user", "organization"
	ResourceType string `json:"resource_type" gorm:"size:50;not null"`

	// Actions define WHAT can be done
	// Examples: ["*"], ["create", "read"], ["read", "validate"]
	Actions pq.StringArray `json:"actions" gorm:"type:text[];not null"`

	// Metadata
	GrantedBy *string    `json:"granted_by,omitempty" gorm:"type:uuid"`
	GrantedAt time.Time  `json:"granted_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`

	// Relations
	User    User  `json:"user" gorm:"foreignKey:UserID"`
	Granter *User `json:"granter,omitempty" gorm:"foreignKey:GrantedBy"`
}

// TableName overrides the table name used by GORM
func (Permission) TableName() string {
	return "permissions"
}

// Permission scopes
const (
	ScopeSystem = "system" // System-wide access
	ScopeOwner  = "owner"  // Owner-based access
)

// Common resource types
const (
	ResourceTypeAll          = "*"
	ResourceTypeOrganization = "organization"
	ResourceTypeProduct      = "product"
	ResourceTypePolicy       = "policy"
	ResourceTypeLicense      = "license"
	ResourceTypeMachine      = "machine"
	ResourceTypeUser         = "user"
	ResourceTypeAPIToken     = "api_token"
	ResourceTypeSession      = "session"
)

// Common actions
const (
	ActionAll      = "*"
	ActionCreate   = "create"
	ActionRead     = "read"
	ActionUpdate   = "update"
	ActionDelete   = "delete"
	ActionValidate = "validate"
	ActionCheckout = "checkout"
)

// IsSystemScope checks if this is a system-wide permission
func (p *Permission) IsSystemScope() bool {
	return p.Scope == ScopeSystem
}

// IsOwnerScope checks if this is an owner-based permission
func (p *Permission) IsOwnerScope() bool {
	return p.Scope == ScopeOwner
}

// IsOrgScope checks if this is an organization-scoped permission
func (p *Permission) IsOrgScope() bool {
	return len(p.Scope) > 4 && p.Scope[:4] == "org:"
}

// IsResourceScope checks if this is a specific resource permission
func (p *Permission) IsResourceScope() bool {
	return len(p.Scope) > 9 && p.Scope[:9] == "resource:"
}

// GetOrgID extracts organization ID from org scope
// Returns empty string if not an org scope
func (p *Permission) GetOrgID() string {
	if !p.IsOrgScope() {
		return ""
	}
	return p.Scope[4:] // Remove "org:" prefix
}

// GetResourceInfo extracts resource type and ID from resource scope
// Returns empty strings if not a resource scope
func (p *Permission) GetResourceInfo() (resourceType, resourceID string) {
	if !p.IsResourceScope() {
		return "", ""
	}

	// Format: "resource:type:id"
	parts := splitScope(p.Scope)
	if len(parts) >= 3 {
		return parts[1], parts[2]
	}
	return "", ""
}

// HasAction checks if permission allows a specific action
func (p *Permission) HasAction(action string) bool {
	if len(p.Actions) == 0 {
		return false
	}

	// Check for wildcard permission
	for _, allowedAction := range p.Actions {
		if allowedAction == ActionAll {
			return true
		}
		if allowedAction == action {
			return true
		}
	}

	return false
}

// MatchesResourceType checks if permission applies to a resource type
func (p *Permission) MatchesResourceType(resourceType string) bool {
	return p.ResourceType == ResourceTypeAll || p.ResourceType == resourceType
}

// IsExpired checks if the permission has expired
func (p *Permission) IsExpired() bool {
	if p.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*p.ExpiresAt)
}

// IsActive checks if the permission is currently active (not expired)
func (p *Permission) IsActive() bool {
	return !p.IsExpired()
}

// Helper functions

// splitScope splits a scope string by colons
func splitScope(scope string) []string {
	result := []string{}
	current := ""

	for _, char := range scope {
		if char == ':' {
			result = append(result, current)
			current = ""
		} else {
			current += string(char)
		}
	}

	if current != "" {
		result = append(result, current)
	}

	return result
}

// BuildOrgScope creates an organization scope string
func BuildOrgScope(orgID string) string {
	return "org:" + orgID
}

// BuildResourceScope creates a resource scope string
func BuildResourceScope(resourceType, resourceID string) string {
	return "resource:" + resourceType + ":" + resourceID
}

// PermissionBuilder helps build permissions easily
type PermissionBuilder struct {
	userID uuid.UUID
}

// NewPermissionBuilder creates a new permission builder
func NewPermissionBuilder(userID uuid.UUID) *PermissionBuilder {
	return &PermissionBuilder{userID: userID}
}

// SystemAdmin creates system-wide admin permission
func (pb *PermissionBuilder) SystemAdmin() *Permission {
	return &Permission{
		UserID:       pb.userID,
		Scope:        ScopeSystem,
		ResourceType: ResourceTypeAll,
		Actions:      pq.StringArray{ActionAll},
		GrantedAt:    time.Now(),
	}
}

// OrgAdmin creates organization admin permission
func (pb *PermissionBuilder) OrgAdmin(orgID string) *Permission {
	return &Permission{
		UserID:       pb.userID,
		Scope:        BuildOrgScope(orgID),
		ResourceType: ResourceTypeAll,
		Actions:      pq.StringArray{ActionAll},
		GrantedAt:    time.Now(),
	}
}

// OrgResource creates permission for specific resource type in organization
func (pb *PermissionBuilder) OrgResource(orgID, resourceType string, actions ...string) *Permission {
	return &Permission{
		UserID:       pb.userID,
		Scope:        BuildOrgScope(orgID),
		ResourceType: resourceType,
		Actions:      pq.StringArray(actions),
		GrantedAt:    time.Now(),
	}
}

// SpecificResource creates permission for specific resource
func (pb *PermissionBuilder) SpecificResource(resourceType, resourceID string, actions ...string) *Permission {
	return &Permission{
		UserID:       pb.userID,
		Scope:        BuildResourceScope(resourceType, resourceID),
		ResourceType: resourceType,
		Actions:      pq.StringArray(actions),
		GrantedAt:    time.Now(),
	}
}

// Owner creates owner-based permission
func (pb *PermissionBuilder) Owner(resourceType string, actions ...string) *Permission {
	return &Permission{
		UserID:       pb.userID,
		Scope:        ScopeOwner,
		ResourceType: resourceType,
		Actions:      pq.StringArray(actions),
		GrantedAt:    time.Now(),
	}
}
